#!/bin/sh

RED='\033[38;5;204m'      # Soft red
GREEN='\033[38;5;114m'    # Muted green  
YELLOW='\033[38;5;180m'   # Warm yellow
BLUE='\033[38;5;75m'      # Calm blue
PURPLE='\033[38;5;141m'   # ATOM purple
CYAN='\033[38;5;73m'      # Muted cyan
ORANGE='\033[38;5;209m'   # Soft orange
GRAY='\033[38;5;145m'     # Light gray
BOLD='\033[1m'
DIM='\033[2m'
NC='\033[0m'

# Get script directory (POSIX compatible)
SCRIPT_DIR=$(dirname "$0")
SCRIPT_DIR=$(cd "$SCRIPT_DIR" && pwd)

#################################
###     HELPER FUNCTIONS      ###
#################################
header() {
    clear
    printf "\n"
    printf "%b================================================================================%b\n" "$PURPLE$BOLD" "$NC"
    printf "%b                            WXD INGEST BUILD SCRIPT                             %b\n" "$PURPLE$BOLD" "$NC"
    printf "%b================================================================================%b\n" "$PURPLE$BOLD" "$NC"
    printf "\n"
}
complete() {
    printf "\n"
    printf "%b================================================================================%b\n" "$GREEN$BOLD" "$NC"
    printf "%b                              SETUP COMPLETE                              %b\n" "$GREEN$BOLD" "$NC"
    printf "%b================================================================================%b\n" "$GREEN$BOLD" "$NC"
    printf "\n"
}
section() {
    printf "\n"
    printf "%b[*] %s%b\n" "$BLUE$BOLD" "$1" "$NC"
    printf "%b========================================================================%b\n" "$BLUE" "$NC"
}
success() {
    printf "%b[✓] %s%b\n" "$GREEN" "$1" "$NC"
}
warning() {
    printf "%b[!] %s%b\n" "$ORANGE" "$1" "$NC"
}
info() {
    printf "%b[>] %s%b\n" "$CYAN" "$1" "$NC"
}
error() {
    printf "%b[x] %s%b\n" "$RED" "$1" "$NC"
}

#################################
#################################




# Copy wxdingest artifacts
copy_wxd_ingest() {
    # Determine and make target directory
    wxdingest_dir="$1/wxd-ingest"
    if [ ! -d "$wxdingest_dir" ]; then
        mkdir "$wxdingest_dir/"
    fi    
    info "Copying wxd-ingest artifact to $wxdingest_dir ..."
    cp -R "wxdingest" "$wxdingest_dir/"
    cp -R "config" "$wxdingest_dir/"
    cp -R "setup.py" "$wxdingest_dir/"
    cp -R "requirements.txt" "$wxdingest_dir/"
}

# Copy scrapydweb artifacts
copy_scrapydweb() {
    # Determine and make target directory
    scrapydweb_dir="$1/scrapydweb"
    if [ ! -d "$scrapydweb_dir" ]; then
        mkdir "$scrapydweb_dir/"
    fi
    info "Copying scrapydweb artifact to $scrapydweb_dir ..."
    cp -R "$2/scrapydweb" "$scrapydweb_dir/"
    cp -R "$2/setup.py" "$scrapydweb_dir/"
    cp -R "$2/README.md" "$scrapydweb_dir/"
    cp -R "$2/MANIFEST.in" "$scrapydweb_dir/"
}


# Copy files needed to build docker container
copy_dockerfiles() {
    info "Copying docker files $1 ..."
    cp -R "container" "$1/"
    #cp "container/Dockerfile" "$1/"
}

build_docker(){
    info "Building docker image in $1 ..."
    docker build -t "wxdingest" -f "$1/container/Dockerfile" "$1" 
}

# Main execution
main() {
    header
    
    info "Script location: $SCRIPT_DIR"

    ### INPUT VALIDATION

    if [ $# -le 1 ]; then
        echo "You need to supply the (1) target directory and (2) the UI source directory"
        return 1
    fi
    if ! [ -d "$1" ]; then
        echo "Target directory $1 does not exist."
        return 1
    fi
    if ! [ -d "$2" ]; then
        echo "UI directory $2 does not exist."
        return 1
    fi


    ### BUILD STEPS

    # (1) Copy the WXD ingest artifacts to the wxd-ingest directory
    copy_wxd_ingest "$@"

    # (2) Copy the customized scrapydweb repo files to the scrapydweb directory
    copy_scrapydweb "$@"

    # (3) Copy the files needed for building the docker container
    copy_dockerfiles "$@"

    # (4) Build docker container image
    build_docker "$1"

    complete
}

# Run it
main "$@"