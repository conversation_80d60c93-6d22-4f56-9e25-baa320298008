

#####################################
###  WATSONX DISCOVERY CONNECTION ###
#####################################

ELASTICSEARCH_URL=https://wx-discovery.ccca.ams1907.com
ELASTICSEARCH_USER_NAME=ingestservice
ELASTICSEARCH_PASSWORD=<REQUIRED>


#####################################
###     WXDIINGEST PARAMETERS     ###
#####################################

# The wdxdingest configs will be copied to this directory
CONFIGS_BASEPATH=/config

# PDF description generation parameters
PDF_DESCRIPTION_MODEL=ibm/granite-3-2-8b-instruct
PDF_PAGES_COUNT=3

# watsons.ai credentials
WATSONX_BASE_URL=https://cpd.ccca.ams1907.com/ml/v1
WATSONX_API_TOKEN=<REQUIRED>
WATSONX_PROJECT_ID=3c917cf9-1fbb-4327-8974-db247673f3c1


#################################
###        SCRAPYDWEB         ###
#################################

# This is where scrapyd will store jobs, etc.
SCRAPYDWEB_DATA_PATH=/scrapyd/scrapydweb_data

# Holds the SAML configuration files
# - settings.json
# - advanced_settings.json
# These files will typically be provided in the config map.
SCRAPYDWEB_SAML_CONFIG_PATH=/saml

# Turn SAML on/off. Default is true
#SCRAPYDWEB_ENABLE_SAML=true

# The attribute by which a user access is controlled
#SCRAPYDWEB_SAML_GROUP_ATTRIBUTE_NAME=group

# The value that attribute must have to be granted access
#SCRAPYDWEB_SAML_ALLOWED_GROUPS=crawleradmin

# Configure basic auth to allow SAML to load the UI
SCRAPYDWEB_USER=admin
SCRAPYDWEB_PASSWORD=p@ssw0rd