# Custom Web Crawler Deployment Instructions for an IBM watsonx Discovery (wxD) Instance at United Parcel Service, Inc. (UPS)

[v0.0.2 Aug 13, 2025]


| **DISCLAIMER** |
|---|
| **This document is a supplement to, and not a replacement for, official documentation. Although the information in this document has been assembled to the best of the authors' (contributors') knowledge, we disclaim any liability with the future use of this information. The owners of the solution must delegate responsibility to the appropriate teams so that documentation can maintain its recency. Please refer to official documentation and/or contact your IBM representative for clarifications and/or additional information.** |
| **This document only covers the deployment of Custom Crawler resources to an OpenShift cluster to make the application available for use.** |
|**CUSTOM CRAWLER**:<br><br>**THE CUSTOM CRAWLER AND ALL ITS CODE (THE OPEN-SOURCE BASE CODE AND ANY CUSTOM/MODIFIED CODE) COME WITHOUT WARRANTY OF ANY KIND. USE AT YOUR OWN RISK. THE AUTHOR(S) AND IBM ASSUME NO RESPONSIBILITY IN ANY WAY FOR ERRORS, ISSUES, AND/OR PROBLEMS CAUSED BY THE EXECUTION OF THIS APPLICATION NOR DO THEY ASSUME OWNERSHIP, LICENSE, AND/OR SUPPORT IN THE USE OF THE CODE INCLUDED IN THE APPLICATION. THE INFORMATION CONTAINED IN THE APPLICATION IS PROVIDED ON AN "AS IS" BASIS AND MAINTENANCE, FIXES, AND UPDATES ARE THE RESPONSIBILITY OF THE INHERITED PARTIES.**|


# Overview

Elasticsearch (ES)/IBM watsonx Discovery (wxD) comes with a feature that allows users to deploy Elastic Crawler (a web crawler) instances. To view deployed instances or to access the page that allows users to create a new one, follow these steps:

1. From the `Home` page, click the navigation menu (three horizontal lines in the top-left area of the page) and click the `Content` link that can be foudn in the `Search` expandable section [of the menu].

2. After the `Elasticsearch Indices` page finishes loading, find the `Web crawlers` link on the left side of the page (navigation menu) and click it.

3. This should load the `Elasticsearch web crawlers` page.


Unfortuantely, the Elastic Crawler has too many limitations to be used by the UPS Search Component Development Team and a custom crawler had to be developed - which will be referred to as, "Custom Crawler". The key factors driving this decision are:

1. Crawl configs cannot be exported/imported from/to other environments. This would increase configuration management complexity and add to the risk of configuration drift across environments.
2. Elasticsearch doesn't provide any features that enable version control capabilities for crawler configuration(s). This would increase configuration management complexity.
3. Not all crawl configuration fields required by UPS are supported OOTB. This doesn't meet UPS' functional requirements and would decrease performance of the proposed solution (i.e. `no-follow` and `no-index` are not supported).


The Custom Crawler incorporates and extends code originally derived from the following open-source projects (with modifications):

* <a href="https://github.com/scrapy/scrapyd">scrapy/scrapyd</a>
	
	Used as a reference for how to write the custom crawler code for UPS.
	
* <a href="https://github.com/my8100/scrapydweb">my8100/scrapydweb</a>

	Used to generate the UI for the custome crawler. Significant modification was adding SAML SSO to the UI.

The Custom Crawler application has three important components:

* Crawler python code.
* Scrapyd server.
* Scrapydweb UI.


## Environment and Activity Description

The tasks in this document are written to be performed/executed from the bastion host of the target OpenShift Container Platform (OCP) cluster, unless stated otherwise.

> **NOTE**: UPS is using OpenShift Dedicated (OSD) 4.17.x with Workload Identity Federation (WIF) on the Google Cloud Platform (GCP) which is a managed-OCP offering provided by Red Hat, Inc. UPS has deployed three private OSD clusters; one non-production and two production (active-active). These clusters, and the bastion host, have public internet access (egress) but only select OSD applications are exposed for public access (ingress) via custom OCP Ingress Controllers.


# Deploying the Custom Crawler

Deploying an instance of the Custom Crawler, on a "fresh" OCP clsuter, includes the following activities:

1. Push the image to the private registry.
2. Deploy an instance of the crawler for a specific wxD instance.
3. Execute post-deployment configuration tasks.


## Pre-requisites

1. Bastion host and admin OSD cluster access.

2. Private registry read/write access. 

3. The cluster must be configured to pull images from the target private registry that will host the crawler image.

	> **NOTE**: The OSD clusters will be pulling Custom Crawler image(s) from the **Google Artifact Registry (GAR)**.

4. A built crawler image has been uploaded to <a href="https://ibm.ent.box.com/folder/330198143682">custom\_crawler\_images</a> (full path: `External Client Working Area - 'WEX Replacement ups.com Search' use case` -> `Artifacts` -> `custom_crawler_images`) IBM/UPS Box folder.

	If the image was built on an local machine, then run the following command to save the image as a compressed file before uploading to Box:
	
		podman save --format docker-archive -o wxd-ingest-crawler_v<version_number>.tar <image_id>

5. Known instance routes (hostnames) and ingress IPs (for DNS record and firewall rule creation, wherever applicable).

6. TLS certificate, TLS key (unencrypted), intermediate certificate(s), and root CA certificate (PEM format).

	> **NOTE**: The certificate file **MUST** contain the full chain of certificates; with the server's certificate first, followed by any intermediate certificates, adn finally the root Certificate Authority (CA) certificate.

7. Prepared deployment YAML files:

	* `ingest-config-cm.yaml` (ingest config file)
	* `ingest-saml-cm.yaml` (SAML config file for SSO)
	* `ingest-svc.yaml` (service definition file to expose OCP StatefulSet internally)
	* `ingest-sts.yaml` (Custom Crawler application specification/configuration)

8. An OSD cluster with the following programs/instances already deployed:

	* watsonx Discovery (specifically Elasticsearch)
		* The instance must contain an index named `spider-configs` that contains configurations for every crawler Python Class defined in the Custom Crawler image.
		
			> **NOTE**: Consult Custom Crawler documentation that was provided by the Delivery Team.
			>
			> * UPSers [and IBMers] can find this in various README files within the latest code that was shared [on July 15 2025] via Box: <a href="https://ibm.ent.box.com/file/1799993629446?s=********************************">wxd-ingest-dev.zip</a>. **UPS is responsible for maintaining the documentation as crawler code is modified.**
			>
			> IBMers can access the internal git repo used to generate the shared zip file: <a href="https://github.ibm.com/TEL-UPS/wxd-ingest">TEL-UPS/wxd-ingest</a>
			
		* (Optional) A dedicated user and password configured for the Custom Crawler application.
	* watsonx.ai
		* At least one LLM model deployed. The model that is defined in the Custom Crawler's `PDF_DESCRIPTION_MODEL` environment variable must be deployed in the target watsonx.ai instance.

9. For each crawler instance, the following information must be known so that environment variables can be created (in the `wxd-api-secret`):

	| Variable | Sample Value | Purpose |
	|---|---|---|
	| ELASTICSEARCH\_URL | https://wx-discovery.ccca.ams1907.com | This is the Elasticsearch cluster that the crawler is interacting with. |
	| ELASTICSEARCH\_USER\_NAME | ingestservice | This is the user name that identifies the crawler instance. |
	| ELASTICSEARCH\_PASSWORD | 49o8hgrbiaro9m*poe-123 | This is the password that the crawler uses to authenticate to the Elasticsearch cluster. |
	| SCRAPYDWEB\_PASSWORD | RthnngsU6JxB2ptoSTbk | This establishes a way for the application to authenticate itself when running scheduled jobs. |
	| WATSONX\_BASE\_URL | https://cpd.ccca.ams1907.com/ml/v1 | PDF ingestion includes a pre-processing step that uses a watsonx.ai LLM to generate a document description that is used as the document description in each PDF document record of the respective index. |
	| WATSONX\_API\_TOKEN | \<really\_long\_string\> | Authentication... |
	| WATSONX\_PROJECT\_ID | 3c917cf9-1fbb-4327-8974-db247673f3c1 | This is a required parameter when making API calls to interact with an LLM dpeloyed on the watsonx.ai platform. |

10. The application, its users, and their respective groups/roles registered in Azure Entra ID with the UPS Identity Management Group/Team.


## Push the image to the private registry

1. Upload the saved crawler image to the bastion host.
2. Load the saved image to the local container storage directory (using either rootful or rootless podman).

		podman load -i wxd-ingest-crawler_v<version_number>.tar

3. Tag the loaded image.

	>**NOTE**: Deployments will be strictly controlled by image versions. Tag all images using a version number and never use the `latest` tag.

4. Log into the GAR repository.
5. Push the image to the GAR repository.


## Deploy an instance of the crawler for a specific wxD instance

A dedicated crawler is deployed for every instance of wxD. Each crawler is deployed into a dedicated OCP project that uses the following naming convention: `<wxd_ns>-ingest`. In other words, the new project name uses the project name of its paired wxD instance and appends the `-ingest` suffix to it.

1. Create the Custom Crawler instance project.

		oc new-project <wxd_ns>-ingest

2. Retrieve the project's uid-range and use the default id to set the value for the `.spec.template.spec.containers[ingestwui].securityContext.fsGroup` field in the `ingest-sts.yaml` file

		oc get namespace <wxd_ns>-ingest \
		-o jsonpath='{.metadata.annotations.openshift\.io/sa\.scc\.uid-range}' | cut -d'/' -f1

3. Create the `ingest-config` ConfigMap.

		oc apply -f ingest-config-cm.yaml -n <wxd_ns>-ingest

4. Create the `saml-configmap` ConfigMap.

		oc apply -f ingest-saml-cm.yaml -n <wxd_ns>-ingest

5. Create the TLS Secret.

		oc create secret tls <tls_secret_name> \
		--cert=<fullchain_certificate>.crt \
		--key=<tls>.key \
		-n <wxd_ns>-ingest

6. Create the `wxd-api-secret` Secret.

		oc create secret generic wxd-api-secret \
		--from-literal=ELASTICSEARCH_URL=<value1> \
		--from-literal=ELASTICSEARCH_USER_NAME=<value2> \
		--from-literal=ELASTICSEARCH_PASSWORD=<value3> \
		--from-literal=SCRAPYDWEB_PASSWORD=<value4> \
		--from-literal=WATSONX_BASE_URL=<value5> \
		--from-literal=WATSONX_API_TOKEN=<value6> \
		--from-literal=WATSONX_PROJECT_ID=<value7> \
		-n <wxd_ns>-ingest

	Example:
	
		oc create secret generic wxd-api-secret \
		--from-literal=ELASTICSEARCH_URL=https://wx-discovery.ccca.ams1907.com \
		--from-literal=ELASTICSEARCH_USER_NAME=ingestservice \
		--from-literal=ELASTICSEARCH_PASSWORD=49o8hgrbiaro9m*poe-123 \
		--from-literal=SCRAPYDWEB_PASSWORD=RthnngsU6JxB2ptoSTbk \
		--from-literal=WATSONX_BASE_URL=https://cpd.ccca.ams1907.com/ml/v1 \
		--from-literal=WATSONX_API_TOKEN=<really_long_string> \
		--from-literal=WATSONX_PROJECT_ID=3c917cf9-1fbb-4327-8974-db247673f3c1 \
		-n ups-wxd-dev-ingest

7. Create the Custom Crawler StatefulSet and watch the pod status for errors.

		oc apply -f ingest-sts.yaml

8. Create the Custom Crawler Service.

		oc apply -f ingest-svc.yaml

9. Create the Custom Crawler Route using the the predetermined hostname. Patch the route with TLS specs. Add the appropriate label to the route so that traffic is routed correctly.

	a. Expose the service.
	
		oc expose svc <service_name> \
		--name <route_name> \
		--hostname <hostname_found_in_tls_cert_or_SAN> \
		-n <wxd_ns>-ingest

	b. Patch the route with TLS specs.
	
		oc patch route <route_name> -n <wxd_ns>-ingest \
		--type=merge \
		-p '{"spec":{"tls":{"termination":"passthrough"}}}'
	
	c. Label the route (if traffic will be routed through the non-default Ingess Controller).

		oc label route <route_name> <label_key>=<label_value> -n <wxd_ns>-ingest


# Supplemental Guidance

This section has the following additional guidance that is related to the continued use of the product:

* Replacing TLS Certificates
* Updating the Deployed Custom Crawler Image Version
* Changing Custom Crawler Instance Host Aliases (In Non-Prod)
* Overriding the Default Request Headers Used for Web Scraping
* Overriding Other Default Environment Variables


## Replacing TLS Certificates

To avoid breaks in trust, certificates should be rotated before expiration.

> **NOTE**: The wxD and Custom Crawler instances use a SAN certificate for each environment, non-prod and prod, to simplify certificate management. As a result, certificates for wxD and Custom Crawler instances will done in tandem.

1. Obtain a new TLS certificate and key (and other certificates in the chain, if appliacable).
2. Create a full chain certificate in PEM format (if it doesn't exist, already).

	> **NOTE**: Make sure that the certificate contains the certificate in the correct order; starting with the server's certificate first, followed by any intermediate certificates, and finally the root Certificate Authority (CA) certificate.

3. Create a new tls secret in the Custom Crawler instance OSD project.
4. Update the `ingest-sts.yaml` file, replacing the old secret name value in `spec.template.spec.volumes[name:<env>-scrapydweb-tls-volume].secret.secretName` with the new secret name.
5. Apply the new yaml file [in the appropriate OSD project] and watch the pod status for errors.

	> **NOTE**: This will restart the stateful set pod. Make sure to apply the change when there are **NO ACTIVE JOBS** in the pod.
	
6. Share the new certificate with the Entra ID team for their corresponding registered application(s).


## Updating the Deployed Custom Crawler Image Version

As new requirements surface, the Custom Crawler image version might need to be updated. If a newer image version needs to be deployed, then following these steps.

1. Build and/or push the new container image to the appropriate location in the Google Artifact Registry.

	>**NOTE**: Deployments will be strictly controlled by image versions. Tag all images using a version number and never use the `latest` tag.

2. Update the `.spec.template.spec.contaienrs[name: ingestwui].image` field in the `ingest-sts.yaml` file.

	>**NOTE**: Deployments will be strictly controlled by image versions. Tag all images using a version number and never use the `latest` tag.

3. Apply the new yaml file [in the appropriate OSD project] and watch the pod status for errors.

	> **NOTE**: This will restart the stateful set pod. Make sure to apply the change when there are **NO ACTIVE JOBS** in the pod.


## Changing Custom Crawler Instance Host Aliases (In Non-Prod)

Non-production Custom Crawler instances are configured to 'crawl'/scrape content from private UPS servers. For some use cases, UPS developers access private environments by modifying the hosts file on their local machines. Host aliases for the Custom Crawlers are used to produce a similar behavior.

If hosts information needs to chagned/updated, then simply modify the entries in the `.spec.template.spec.hostAliases` section of the `ingest-sts.yaml` file.


## Overriding the Default Request Headers Used for Web Scraping

During the initial delivery engagement, the development teams experienced the following problems, which required the modification of the original HTTP request headers.

* PROBLEM: Akamai throttling/blocking Custom Crawler content scraping requests.

	RESOLUTION: IBM Delivery, UPS Search, and UPS Akamai Teams (Lionel Andre) agreed to use a custom header, called `'X-UPS-WatsonSearch'`, and value to identify the Custom Crawler as an approved agent for scraping content from Akamai hosted/delivered pages.

* PROBLEM: UPS servers and/or proxies sending brotli-encoded responses that made crawler logs show the following error" `brotli.error: BrotliDecoderDecompressStream failed while processing the stream`, preventing content extraction.

	RESOLUTION: Explicitly define the values for the `'Accept-Encoding'` header for HTTP scraping requests and exclude the `'br'` (Brotli) encoding value from the list. Current key and values:
	
	`'Accept-Encoding': 'gzip, deflate'`

The default HTTP request headers shouldn't need to be changed, but in case they do, new headers can be 'injected' using a Config Map (avoiding the nuisance of having to build a new container image for such a change).

To override the default crawler request headers, perform the following steps:

1. Create a crawler-headers-config ConfigMap in the appropriate Custom Crawler OSD project.
2. Update the `.spec.template.spec.containers[name: ingestwui].env[name: CRAWLER_HEADERS]` section in the `ingest-sts.yaml` file, specifically removing the `optional: true` line nested under `.valueFrom.configMapKeyRef` while making sure that the name matches the name of the ConfigMap that was recently created.
3. Apply the new yaml file [in the appropriate OSD project] and watch the pod status for errors.

	> **NOTE**: This will restart the stateful set pod. Make sure to apply the change when there are **NO ACTIVE JOBS** in the pod.



## Overriding Other Default Environment Variables

Other environment variables can be modified similarly to what has already been described in the above section. It is recommended that a knowledgeable individual be assigned the planning and implementation of such requests.

Some environment variables that might need to be changed in the future:

* `PDF_DESCRIPTION_MODEL` (Default: `ibm/granite-3-2-8b-instruct` - This value can be obtained from the watsonx.ai UI.)
* `PDF_INDEX` (Default: `true` - Boolean flag that instructs the crawler to store PDF documents in a separate index with a `-pdf` suffix.



# Deployment Variation: Deploying w/ BASIC AUTH instead of SAML SSO

If a Custom Crawler instance has to be deployed before an application can be registered with the Azure Entra ID team, then the application can be deploying using a temporary admin user (until SAML SSO can be configured). To do so, perform the same deployment steps as described above, with the following exceptions:

1. Do not create the `saml-configmap`.
2. Edit the `ingest-sts.yaml` file, removing references to the saml confimap.
3. Create the `wxd-api-secret` with the following environment variables (adding one new environment variable):

	| Variable | Sample Value | Purpose |
	|---|---|---|
	| ELASTICSEARCH\_URL | https://wx-discovery.ccca.ams1907.com | This is the Elasticsearch cluster that the crawler is interacting with. |
	| ELASTICSEARCH\_USER\_NAME | ingestservice | This is the user name that identifies the crawler instance. |
	| ELASTICSEARCH\_PASSWORD | 49o8hgrbiaro9m*poe-123 | This is the password that the crawler uses to authenticate to the Elasticsearch cluster. |
	| SCRAPYDWEB\_PASSWORD | RthnngsU6JxB2ptoSTbk | This establishes a way for the application to authenticate itself when running scheduled jobs. |
	| WATSONX\_BASE\_URL | https://cpd.ccca.ams1907.com/ml/v1 | PDF ingestion includes a pre-processing step that uses a watsonx.ai LLM to generate a document description that is used as the document description in each PDF document record of the respective index. |
	| WATSONX\_API\_TOKEN | \<really\_long\_string\> | Authentication... |
	| WATSONX\_PROJECT\_ID | 3c917cf9-1fbb-4327-8974-db247673f3c1 | This is a required parameter when making API calls to interact with an LLM dpeloyed on the watsonx.ai platform. |
	| SCRAPYDWEB\_ENABLE\_AUTH | true | Enables basic authentication on the UI. |
	
	Example:
	
		oc create secret generic wxd-api-secret \
		--from-literal=ELASTICSEARCH_URL=https://wx-discovery.ccca.ams1907.com \
		--from-literal=ELASTICSEARCH_USER_NAME=ingestservice \
		--from-literal=ELASTICSEARCH_PASSWORD=49o8hgrbiaro9m*poe-123 \
		--from-literal=SCRAPYDWEB_PASSWORD=RthnngsU6JxB2ptoSTbk \
		--from-literal=WATSONX_BASE_URL=https://cpd.ccca.ams1907.com/ml/v1 \
		--from-literal=WATSONX_API_TOKEN=<really_long_string> \
		--from-literal=WATSONX_PROJECT_ID=3c917cf9-1fbb-4327-8974-db247673f3c1 \
		--from-literal=SCRAPYDWEB_ENABLE_AUTH=true \
		-n ups-wxd-dev-ingest

	> **NOTE**: The default username for login is `admin`. If that needs to be chagned, then add one more variable to the `wxd-api-secret` secret by assigning the desired username string to the `SCRAPYDWEB_USER` key.
	
	> Example additional parameter:
	
	>		--from-literal=SCRAPYDWEB_USER=crawlerAdmin

# Appendix: Pre-requisite YAML Files Reference

**Consult Custom Crawler docs for information for advanced configurations/settings.**

## `ingest-config-cm.yaml` (ConfigMap) Example

	apiVersion: v1
	data:
	  CONFIGS_BASEPATH: /config
	  SCRAPYDWEB_ENABLE_HTTPS: "true"
	  SCRAPYDWEB_CERTIFICATE_FILEPATH: "/certs/tls.crt"
	  SCRAPYDWEB_PRIVATEKEY_FILEPATH: "/certs/tls.key"
	  SCRAPYDWEB_DATA_PATH: "/scrapyd/scrapydweb_data"
	kind: ConfigMap
	metadata:
	  name: ingest-config
	  namespace: ups-wxd-dev-ingest

This file will likely only need to have the namespace field changed for each deployment.


## `ingest-saml-cm.yaml` (ConfigMap) Example

	apiVersion: v1
	kind: ConfigMap
	metadata:
	  name: saml-configmap
	  namespace: ups-wxd-dev-ingest
	data:
	  SCRAPYDWEB_ENABLE_SAML: "true"
	  SCRAPYDWEB_SAML_CONFIG_PATH: "/saml"
	  SCRAPYDWEB_SAML_SESSSION_TIMEOUT: "60"
	  SCRAPYDWEB_SAML_GROUP_ATTRIBUTE_NAME: "http://schemas.microsoft.com/ws/2008/06/identity/claims/role"
	  SCRAPYDWEB_SAML_ALLOWED_GROUPS: "DevAdmin"
	  advanced_settings.json: |
	    {
	        "security": {
	            "requestedAuthnContext": false,
	            "failOnAuthnContextMismatch": false
	        }
	    }
	  settings.json: |
	    {
	        "strict": false,
	        "debug": true,
	        "sp": {
	          "entityId": "https://ingest-dev-ui.ccca.ams1907.com",
	          "assertionConsumerService": {
	            "url": "https://ingest-dev-ui.ccca.ams1907.com/acs/",
	            "binding": "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
	          },
	          "singleLogoutService": {
	            "url": "https://ingest-dev-ui.ccca.ams1907.com/sls/",
	            "binding": "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
	          },
	          "NameIDFormat": "urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified"
	        },
	        "idp": {
	          "entityId": "https://sts.windows.net/e7520e4d-d5a0-488d-9e9f-949faae7dce8/",
	          "singleSignOnService": {
	            "url": "https://login.microsoftonline.com/e7520e4d-d5a0-488d-9e9f-949faae7dce8/saml2",
	            "binding": "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
	          },
	          "singleLogoutService": {
	            "url": "https://login.microsoftonline.com/e7520e4d-d5a0-488d-9e9f-949faae7dce8/saml2",
	            "binding": "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
	          },
	          "x509cert": "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"
	        }
	      }

Edit contents of this file accordingly.

## `ingest-sts.yaml` (StatefulSet) Example

	apiVersion: apps/v1
	kind: StatefulSet
	metadata:
	  annotations:
	  name: ingest-dev
	  namespace: ups-wxd-dev-ingest
	spec:
	  replicas: 1
	  selector:
	    matchLabels:
	      app: ingest-sts
	  serviceName: ingest-dev
	  template:
	    metadata:
	      labels:
	        app: ingest-sts
	    spec:
	      containers:
	      - envFrom:
	        - secretRef:
	            name: wxd-api-secret
	        - configMapRef:
	            name: ingest-config
	        - configMapRef:
	            name: saml-configmap
	        env:
	        - name: CRAWLER_HEADERS
	          valueFrom:
	            configMapKeyRef:
	              name: crawler-headers-config
	              key: CRAWLER_HEADERS
	              optional: true
	        image: us-docker.pkg.dev/gcp-dct-ccca-dev/ccca-d-image-registry/wex-replacement/ingestwui:1.2.8
	        imagePullPolicy: Always
	        name: ingestwui
	        ports:
	        - containerPort: 5000
	          protocol: TCP
	        volumeMounts:
	        - mountPath: /scrapyd/
	          name: scrapyd-storage
	        - mountPath: /certs
	          name: dev-scrapydweb-tls-volume
	          readOnly: true
	        - mountPath: /saml
	          name: saml-config-volume
	          readOnly: true
	      dnsPolicy: ClusterFirst
	      hostAliases:
	      - hostnames:
	        - www.ups.com
	        - ups.com
	        ip: **************
	      - hostnames:
	        - wwwapps.ups.com
	        - apps.ups.com
	        - developer.ups.com
	        ip: *************
	      - hostnames:
	        - campusship.ups.com
	        - www.campusship.ups.com
	        ip: **************
	      - hostnames:
	        - wwwcie.ups.com
	        - www.cie.ups.com
	        - cie.ups.com
	        ip: **************
	      - hostnames:
	        - filexfer.ups.com
	        - www.filexfer.ups.com
	        ip: **************
	      - hostnames:
	        - webservices.ups.com
	        - www.webservices.ups.com
	        ip: **************
	      - hostnames:
	        - onlinetools.ups.com
	        - www.onlinetools.ups.com
	        ip: *************
	      - hostnames:
	        - www.livesite.ups.com
	        - livesite.ups.com
	        ip: *************
	      - hostnames:
	        - webapis.ups.com
	        ip: **************
	      - hostnames:
	        - es-us.ups.com
	        - es-us-m.ups.com
	        - es-us-apps.ups.com
	        - es-us-campusship.ups.com
	        - si.ups.com
	        - si-m.ups.com
	        - si-apps.ups.com
	        - si-campusship.ups.com
	        - si-filexfer.ups.com
	        - ua.ups.com
	        - ua-m.ups.com
	        - ua-apps.ups.com
	        - ua-campusship.ups.com
	        - ua-filexfer.ups.com
	        - ru.ups.com
	        - ru-m.ups.com
	        - ru-apps.ups.com
	        - ru-campusship.ups.com
	        - ru-filexfer.ups.com
	        ip: *************
	      initContainers:
	      - command:
	        - /bin/sh
	        - -c
	        - mkdir -p /scrapyd/logs /scrapyd/dbs /scrapyd/items /certs
	        - |
	          echo "Checking for TLS Certificates..."
	          if [ ! -f /certs/tls.crt ] || [ ! -f /certs/tls.key ]; then
	            echo "TLS certificate or key not found. Exiting..."
	            exit 1
	          fi
	          echo "TLS certificates exist. Proceeding..."
	          exit 0
	        image: us-docker.pkg.dev/gcp-dct-ccca-dev/ccca-d-image-registry/wex-replacement/busybox
	        imagePullPolicy: Always
	        name: init-scrapyd
	        volumeMounts:
	        - mountPath: /scrapyd/
	          name: scrapyd-storage
	        - mountPath: /certs
	          name: dev-scrapydweb-tls-volume
	          readOnly: true
	      restartPolicy: Always
	      securityContext:
	        fsGroup: 1001320000
	      volumes:
	      - name: dev-scrapydweb-tls-volume
	        secret:
	          defaultMode: 420
	          secretName: fullchain-tls-20260701
	      - configMap:
	          defaultMode: 420
	          items:
	          - key: settings.json
	            path: settings.json
	          - key: advanced_settings.json
	            path: advanced_settings.json
	          name: saml-configmap
	        name: saml-config-volume
	  volumeClaimTemplates:
	  - apiVersion: v1
	    kind: PersistentVolumeClaim
	    metadata:
	      name: scrapyd-storage
	    spec:
	      accessModes:
	      - ReadWriteOnce
	      resources:
	        requests:
	          storage: 64Gi
	      storageClassName: ssd-csi


## `ingest-svc.yaml` (Service) Example

	apiVersion: v1
	kind: Service
	metadata:
	  name: ingest-dev
	  namespace: ups-wxd-dev-ingest
	spec:
	  ports:
	  - port: 5000
	    protocol: TCP
	    targetPort: 5000
	  selector:
	    app: ingest-sts
	  type: ClusterIP


## `ingest-custom_headers-cm.yaml` (ConfigMap) Example

	apiVersion: v1
	kind: ConfigMap
	metadata:
	  name: crawler-headers-config
	  namespace: ups-wxd-dev-ingest
	data:
	  CRAWLER_HEADERS: >
	    {
	      "Accept-Encoding": "gzip, deflate",
	      "accept": "text/html,application/xhtml+xml,application/xml",
	      "accept-language": "en-US,en;q=0.9,de;q=0.8",
	      "cache-control": "max-age=0",
	      "priority": "u=0, i",
	      "sec-ch-ua": "Not(A:Brand\\\";v=\\\"99\\\", \\\"Google Chrome\\\";v=\\\"133\\\", \\\"Chromium\\\";v=\\\"133",
	      "sec-ch-ua-mobile": "?0",
	      "sec-ch-ua-platform": "macOS",
	      "sec-fetch-dest": "document",
	      "sec-fetch-mode": "navigate",
	      "sec-fetch-site": "none",
	      "sec-fetch-user": "?1",
	      "upgrade-insecure-requests": "1",
	      "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
	      "X-UPS-WatsonSearch": "V2F0c29uIFNlYXJjaCBzY3JhcGVy"
	    }


# DOCUMENT HISTORY

| VERSION | DATE | COMMENT |
|---|---|---|
| v.0.0.1 | Aug 12, 2025 | Initial release, submitted for internal review by Essential Management. |
| v.0.0.2 | Aug 13, 2025 | Added links for crawler code that was shared with UPS, submitted for internal review by Essential Management. |