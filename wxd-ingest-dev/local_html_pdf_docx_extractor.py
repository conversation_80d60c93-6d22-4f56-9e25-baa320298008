import os
import hashlib
from datetime import datetime
from bs4 import BeautifulSoup
import html2text
from pypdf import Pdf<PERSON>eader
from docx import Document

from wxdingest.model import IngestDocument
from wxdingest.ingest.ingest_utils import IngestUtils
from wxdingest.elasticsearch_interface import ElasticsearchInterface
from wxdingest.setup.instances_manager import InstancesManager

class LocalHtmlFile:
    def __init__(self, path):
        self.url = f"file://{os.path.abspath(path)}"
        with open(path, encoding="utf-8") as f:
            self.text = f.read()
            self.body = self.text


class HtmlExtractor:
    def __init__(self):
        self.utils = IngestUtils()
    
    def extract(self, html_file) -> IngestDocument:
        try:
            text = html_file.text
        except Exception as e:
            print(f"Could not parse {html_file.url}: {e}")
            return None

        doc = self._extract_document_metadata(html_file)
        doc.body = self._extract_body(html_file)
        doc.file_type = "HTML"
        return doc

    def _extract_document_metadata(self, html_file) -> IngestDocument:
        doc = IngestDocument()
        doc.url = html_file.url
        doc.id = hashlib.md5(doc.url.encode('utf-8')).hexdigest()
        soup = BeautifulSoup(html_file.text, "lxml")
        doc.title = soup.title.string if soup.title else None
        desc = soup.find('meta', attrs={'name':'description'})
        doc.description = desc['content'] if desc and desc.has_attr('content') else None
        kw = soup.find('meta', attrs={'name':'keywords'})
        doc.keywords = kw['content'].split(',') if kw and kw.has_attr('content') else None
        loc = soup.find('meta', attrs={'name':'ups-locale'})
        doc.locale = loc['content'] if loc and loc.has_attr('content') else None
        pub = soup.find('meta', attrs={'name':'published-time'})
        doc.published_time = pub['content'] if pub and pub.has_attr('content') else datetime.utcnow().isoformat() + "Z"
        return doc

    @staticmethod
    def _extract_body(html_file) -> str:
        html = html_file.text
        try:
            h = html2text.HTML2Text()
            h.ignore_links = True
            h.skip_internal_links = True
            h.inline_links = False
            h.ignore_images = True
            h.ignore_emphasis = True
            h.ignore_mailto_links = True
            return h.handle(html)
        except Exception:
            soup = BeautifulSoup(html, features="lxml")
            return soup.get_text()


    
class LocalPdfFile:
    def __init__(self, path):
        self.path = path  # save actual path
        self.url = f"file://{os.path.abspath(path)}"

class PdfExtractor:
    def extract(self, pdf_file_wrapper) -> IngestDocument:
        doc = IngestDocument()
        doc.url = pdf_file_wrapper.url
        doc.id = hashlib.md5(doc.url.encode('utf-8')).hexdigest()
        try:
            with open(pdf_file_wrapper.path, "rb") as f:
                pdf = PdfReader(f)
                texts = []
                for page in pdf.pages:
                    texts.append(page.extract_text() or "")
                doc.body = "\n".join(texts)
            doc.file_type = "PDF"
        except Exception as e:
            print(f"Error extracting PDF {doc.url}: {e}")
            return None
        return doc

class DocxExtractor:
    def __init__(self):
        pass
    
    def extract(self, docx_path) -> IngestDocument:
        try:
            document = Document(docx_path)
        except Exception as e:
            print(f"Error opening docx {docx_path}: {e}")
            return None
        
        doc = IngestDocument()
        doc.url = f"file://{os.path.abspath(docx_path)}"
        doc.id = hashlib.md5(doc.url.encode('utf-8')).hexdigest()
        full_text = []

        for para in document.paragraphs:
            if para.text.strip():
                full_text.append(para.text)
        for table in document.tables:
            rows_text = []
            for row in table.rows:
                cells = [cell.text.strip() for cell in row.cells]
                rows_text.append("\t".join(cells))
            full_text.append("\n".join(rows_text))
        doc.body = "\n".join(full_text)

        core_properties = document.core_properties
        doc.title = core_properties.title or os.path.basename(docx_path)
        doc.keywords = core_properties.keywords
        doc.published_time = core_properties.created.isoformat() if core_properties.created else datetime.utcnow().isoformat() + "Z"
        doc.file_type = "DOCX"
        
        return doc


def crawl_and_ingest(folder_path, elasticsearch_instance, index, pipeline=None):
    html_extractor = HtmlExtractor()
    pdf_extractor = PdfExtractor()
    docx_extractor = DocxExtractor()

    for root, _, files in os.walk(folder_path):
        for f in files:
            file_path = os.path.join(root, f)
            if f.lower().endswith('.html') or f.lower().endswith('.htm'):
                html_file = LocalHtmlFile(file_path)
                doc = html_extractor.extract(html_file)
            elif f.lower().endswith('.pdf'):
                pdf_file = LocalPdfFile(file_path)
                doc = pdf_extractor.extract(pdf_file)
            elif f.lower().endswith('.docx'):
                doc = docx_extractor.extract(file_path)
            else:
                continue
            
            if doc is None:
                continue
            
            try:
                document_dict = doc.to_json()
                res = elasticsearch_instance.ingest_document(
                    document=document_dict,
                    id=doc.id,
                    index=index,
                    pipeline=pipeline
                )
                print(f"Ingested {file_path} successfully: {res}")
            except Exception as e:
                print(f"Error ingesting {file_path}: {e}")


if __name__ == "__main__":
    #from wxdingest.setup.instances_manager import InstancesManager

    instances_manager = InstancesManager()
    elastic = instances_manager.wxd()
    folder_to_crawl = "/Users/<USER>/Downloads/Quantum-Fiber-Active-3 files"
    index = "html-pdf-docx-index"
    crawl_and_ingest(folder_to_crawl, elastic, index)
