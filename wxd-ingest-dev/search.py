from wxdingest.setup.instances_manager import InstancesManager
from wxdingest.elasticsearch_interface import ElasticsearchInterface

def search_documents(elastic: ElasticsearchInterface, index: str, query_text: str, size: int = 3):
    
    query_body = {
        "query": {
            "multi_match": {
                "query": query_text,
                "fields": ["body", "title", "description", "keywords"]
            }
        },
        "size": size
    }

    try:
        response = elastic.query(index=index, query_body=query_body)
        hits = response.get('hits', {}).get('hits', [])
        print(f"Found {len(hits)} matching documents for query '{query_text}':")
        for hit in hits:
            source = hit.get('_source', {})
            title = source.get('title', 'No Title')
            snippet = (source.get('body') or '')[:200].replace('\n', ' ')
            print(f"- {title}: {snippet} ...")
        return hits
    except Exception as e:
        print(f"failed: {e}")
        return []

if __name__ == "__main__":
    instances_manager = InstancesManager()
    elastic = instances_manager.wxd()
    index_name = "html-pdf-docx-shree"

    search_term = "what is the Rewards Status Inquiry Process?"  # Replace with your search keyword or phrase
    search_documents(elastic, index_name, search_term)
